pool:
  name: Azure Pipelines
  vmImage: windows-latest

trigger: none

resources:
  pipelines:
    - pipeline: attane-azure-functions-ci
      source: 'Attane Azure Functions CI'
      trigger: true

steps:
  # azfunc buildの結果を取得
  - task: DownloadPipelineArtifact@2
    displayName: 'azfunc buildの結果を取得'
    inputs:
      buildType: 'specific'
      project: '9cb95b05-26cf-4a68-8814-7e28bdcbb458'
      definition: '33'
      buildVersionToDownload: 'latest'
      targetPath: '$(Pipeline.Workspace)/azfunc'

  # drop内のzipファイルをコピー
  - task: CopyFiles@2
    displayName: 'drop内のzipファイルをコピー'
    inputs:
      sourceFolder: '$(Pipeline.Workspace)/azfunc/drop'
      contents: '*.zip'
      targetFolder: '$(System.DefaultWorkingDirectory)/temp'

  # Az Moduleをインストール
  - powershell: |
      Install-Module -Name Az.Accounts -AllowClobber -Force
      Install-Module -Name Az.Websites -AllowClobber -Force
    displayName: 'PowerShell: Install Az Module'

  #  リリーススクリプトを実行
  - task: PowerShell@2
    displayName: 'PowerShell : Deploy Azure Functions'
    inputs:
      filePath: '$(System.DefaultWorkingDirectory)/deploy/azfunc/deploy-azfunc.ps1'
      arguments: '-Env $(ENV)'
