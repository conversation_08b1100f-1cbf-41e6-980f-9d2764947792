<#
.SYNOPSIS
  Azure AppService内の指定したパスにWorking Directory内のzipファイルを配置します。
#>

param (
    # デプロイしたい環境を指定する。指定は必須。
    [Parameter(Mandatory=$true)][string]$Env
)

# このフォルダの中にzipファイルはデプロイ対象の1つのみである前提。
$publishFile = Get-Item "${env:System_DefaultWorkingDirectory}\temp\*.zip"

. "$($PSScriptRoot)\settings\$($Env)_variables.ps1"

$clientSecret = ConvertTo-SecureString -String $ClientSecret -AsPlainText -Force
$clientCredential = New-Object System.Management.Automation.PSCredential($ClientId, $clientSecret)

Connect-AzAccount -ServicePrincipal -Credential $clientCredential -Tenant $TenantId

$publishProfiles = [xml](Get-AzWebAppPublishingProfile -ResourceGroupName $ResourceGroupName -Name $AppServiceName)
$publishProfile = $publishProfiles.publishData.publishProfile | Where-Object publishMethod -eq 'ZipDeploy'

# Azure Functions の場合、配置先相対パスが異なる
$publishUrl = "https://$($publishProfile.publishUrl)/api/zipdeploy"
$publishUserName = $publishProfile.userName
$publishPassword = $publishProfile.userPWD
$publishToken = [System.Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("${publishUserName}:${publishPassword}"))

Write-Output "Start deploy"

Invoke-RestMethod -Uri $publishUrl -Method POST -Headers @{ Authorization="Basic ${publishToken}" } -InFile $publishFile -ContentType "multipart/form-data"
