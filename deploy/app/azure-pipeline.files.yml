parameters:
  - name: env
    displayName: Build Environment
    type: string

steps:
  # Az Moduleをインストール
  - powershell: |
      Install-Module -Name Az.Accounts -AllowClobber -Force
      Install-Module -Name Az.Websites -AllowClobber -Force
    displayName: 'PowerShell: Install Az Module'

  # execute the PS script to replace the variables.js
  - task: PowerShell@2
    displayName: 'PowerShell : Deploy Config file on Azure App Service'
    inputs:
      filePath: '$(Build.Repository.LocalPath)/deploy/app/deploy-files.ps1'
      arguments: '-Env ${{parameters.env}}'
