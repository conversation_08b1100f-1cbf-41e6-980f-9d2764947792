parameters:
  - name: env
    displayName: Build Environment
    type: string

steps:
  # Web buildの結果を取得
  - task: DownloadPipelineArtifact@2
    displayName: 'Web buildの結果を取得'
    inputs:
      buildType: 'specific'
      project: '9cb95b05-26cf-4a68-8814-7e28bdcbb458'
      definition: '48'
      buildVersionToDownload: 'latest'
      targetPath: '$(Pipeline.Workspace)/web'
      # linter警告を含む可能性があるため追加
      # builds can have linter warnings, but we allow deploying
      allowPartiallySucceededBuilds: true

  # Webのdropをtemp/deployへ展開
  - task: ExtractFiles@1
    displayName: 'Web buildのdropをtemp/deployへ展開'
    inputs:
      archiveFilePatterns: '$(Pipeline.Workspace)/web/drop-dev/*.zip'
      destinationFolder: '$(System.DefaultWorkingDirectory)/temp/deploy'

  # API buildの結果を取得
  - task: DownloadPipelineArtifact@2
    displayName: 'API buildの結果を取得'
    inputs:
      buildType: 'specific'
      project: '9cb95b05-26cf-4a68-8814-7e28bdcbb458'
      definition: '32'
      buildVersionToDownload: 'latest'
      targetPath: '$(Pipeline.Workspace)/api'
      patterns: 'drop/**'
      allowPartiallySucceededBuilds: true


  # APIをtemp/deploy/apiへ展開
  - task: ExtractFiles@1
    displayName: 'API buildのdropをtemp/deploy/apiへ展開'
    inputs:
      archiveFilePatterns: '$(Pipeline.Workspace)/api/drop/*.zip'
      destinationFolder: '$(System.DefaultWorkingDirectory)/temp/deploy/api'

  # 【開発環境専用】
  # Webのstorybookをtemp/deploy/storybookへ展開
  # - ${{ if in(parameters.env, 'dev') }}:
  #   - task: ExtractFiles@1
  #     displayName: '【開発環境専用】 API buildのstorybookをtemp/deploy/storybookへ展開'
  #     inputs:
  #       archiveFilePatterns: '$(Pipeline.Workspace)/web/storybook/*.zip'
  #       destinationFolder: '$(System.DefaultWorkingDirectory)/temp/deploy/storybook'

  # 圧縮ファイルを作成
  - task: ArchiveFiles@2
    displayName: 'Archive files'
    inputs:
      rootFolderOrFile: '$(System.DefaultWorkingDirectory)/temp/deploy'
      includeRootFolder: false
      archiveFile: '$(System.DefaultWorkingDirectory)/temp/deploy.zip'

  # Az Moduleをインストール
  - powershell: |
      Install-Module -Name Az.Accounts -AllowClobber -Force
      Install-Module -Name Az.Websites -AllowClobber -Force
    displayName: 'PowerShell: Install Az Module'

  # リリーススクリプトを実行
  - task: PowerShell@2
    displayName: 'PowerShell : Deploy Azure App Service'
    inputs:
      filePath: '$(System.DefaultWorkingDirectory)/deploy/app/deploy-appservice.ps1'
      arguments: '-Env ${{ parameters.env }} -Archive $(System.DefaultWorkingDirectory)/temp/deploy.zip'
