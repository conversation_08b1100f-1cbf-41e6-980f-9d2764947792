param (
	[Parameter(Mandatory=$true)][string]$Env
)

$Files = @(
	@{
		Label = 'variables'
		Source = "${PSScriptRoot}/../param-files/variables.${Env}.js"
		Target = "/home/<USER>/wwwroot/variables.js"
	}
	@{
		Label = 'config.json'
		Source = "${PSScriptRoot}/../param-files/appsettings.${Env}.json"
		Target = "/home/<USER>/wwwroot/api/appsettings.json"
	}
)

$Files | % {
	& "${PSScriptRoot}\deploy-file.ps1" -Env $Env -source $_.Source -target $_.Target
}
