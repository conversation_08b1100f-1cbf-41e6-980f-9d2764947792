# mec環境用の資源をリリースする
# CDの実行トリガー
trigger: none
resources:
  pipelines:
    - pipeline: attane-mec-devops-ci
      source: 'Attane MEC DevOps CI'
      trigger: true
pool:
      name: Azure Pipelines
      vmImage: windows-latest
steps:
  # Web buildの結果を取得
  - task: DownloadPipelineArtifact@2
    displayName: 'Web buildの結果を取得'
    inputs:
      buildType: 'specific'
      project: '9cb95b05-26cf-4a68-8814-7e28bdcbb458'
      definition: '48'
      buildVersionToDownload: 'latest'
      targetPath: '$(Pipeline.Workspace)/web'
      allowPartiallySucceededBuilds: true
  # API buildの結果を取得
  - task: DownloadPipelineArtifact@2
    displayName: 'API buildの結果を取得'
    inputs:
      buildType: 'specific'
      project: '9cb95b05-26cf-4a68-8814-7e28bdcbb458'
      definition: '32'
      buildVersionToDownload: 'latest'
      targetPath: '$(Pipeline.Workspace)/api'
      allowPartiallySucceededBuilds: true
  # azfunc buildの結果を取得
  - task: DownloadPipelineArtifact@2
    displayName: 'azfunc buildの結果を取得'
    inputs:
      buildType: 'specific'
      project: '9cb95b05-26cf-4a68-8814-7e28bdcbb458'
      definition: '33'
      buildVersionToDownload: 'latest'
      targetPath: '$(Pipeline.Workspace)/azfunc'
      allowPartiallySucceededBuilds: true

  # MEC CIの結果を取得
  - task: DownloadPipelineArtifact@2
    displayName: 'MEC CIの結果を取得'
    inputs:
      buildType: 'specific'
      project: '9cb95b05-26cf-4a68-8814-7e28bdcbb458'
      definition: '46'
      buildVersionToDownload: 'latest'
      targetPath: '$(Pipeline.Workspace)'
  # Artifact成果物のコピー
  - task: CopyFiles@2
    displayName: 'web/dropをコピー'
    inputs:
      SourceFolder: $(Pipeline.Workspace)/web/drop-dev
      Contents: '*.zip'
      TargetFolder: work/web
  - task: CopyFiles@2
    displayName: 'api/drop/mecをコピー'
    inputs:
      SourceFolder: $(Pipeline.Workspace)/api/drop/mec
      Contents: '*.zip'
      TargetFolder: work/api
  - task: CopyFiles@2
    displayName: 'azfunc/dropをコピー'
    inputs:
      SourceFolder: $(Pipeline.Workspace)/azfunc/drop
      Contents: '*.zip'
      TargetFolder: work/azfunc
  - task: CopyFiles@2
    displayName: 'deployをコピー'
    inputs:
      SourceFolder: $(Pipeline.Workspace)/drop/deploy
      Contents: '**'
      TargetFolder: work/deploy
  - task: CopyFiles@2
    displayName: 'arm/dropをコピー'
    inputs:
      SourceFolder: $(Pipeline.Workspace)/drop/arm
      Contents: '**'
      TargetFolder: work/arm
  - task: CopyFiles@2
    displayName: 'manifest/dropをコピー'
    inputs:
      SourceFolder: $(Pipeline.Workspace)/drop/manifest
      Contents: '**'
      TargetFolder: work/manifest
  # アプリのビルド結果をzip圧縮
  - task: ArchiveFiles@2
    displayName: 'workをアーカイブ'
    inputs:
      rootFolderOrFile: work
      includeRootFolder: false
      archiveFile: '$(Build.ArtifactStagingDirectory)/build/$(Build.BuildId).zip'
  # アプリのビルド結果をアーティファクトとしてpublish
  - task: PublishPipelineArtifact@1
    displayName: 'アプリのビルド結果をアーティファクト名dropとしてpublish'
    inputs:
      targetPath: '$(Build.ArtifactStagingDirectory)/build'
      artifact: 'drop'
