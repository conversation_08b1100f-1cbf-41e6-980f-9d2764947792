# mec環境用の資源のうち不要なファイルを削除する
# CIの実行トリガー
trigger:
  tags:
    include:
      - release/sprint*/*
pool:
  name: Azure Pipelines
  vmImage: windows-latest

steps:
  # armディレクトリから必要ファイルをコピー1
  - task: CopyFiles@2
    displayName: 'arm/直下のファイルをコピー'
    inputs:
      SourceFolder: arm
      Contents: |
        azure-pipelines_dev.yml
        azure-pipelines_source.yml
        deploy_resourcegroup.ps1
        deploy_rg_sources.ps1
        params_prd001/parameters_mec.prd001.json
        params_stg001/parameters_mec.stg001.json
        params_prd001/parameters_frontdoor.prd001.json
        params_stg001/parameters_frontdoor.stg001.json
        params_stg001/parameters_rg_sources.stg001.json
        params_prd001/parameters_rg_sources.prd001.json
        params_prd001/parameters_share.prd001.json
        params_stg001/parameters_share.stg001.json
        params_prd001/parameters_share_dep.prd001.json
        params_stg001/parameters_share_dep.stg001.json
        params_prd001/parameters_frontdoor_alert.prd001.json
        params_stg001/parameters_frontdoor_alert.stg001.json
        params_prd001/parameters_mec_alert.prd001.json
        params_stg001/parameters_mec_alert.stg001.json
        params_prd001/parameters_share_alert.prd001.json
        params_stg001/parameters_share_alert.stg001.json
        template_rg-sources.json
        template_share_dep.json
        template_share.json
        template_share_alert.json
        template_share_frontdoor.json
        template_share_frontdoor_alert.json
        template_unique.json
        template_unique_alert.json
        Register-ResourceProviders.ps1
        Restart.ps1
      TargetFolder: $(Build.ArtifactStagingDirectory)/build/arm
  # yamlファイル名を変更1 (devと入っているので削除) 
  - task: PowerShell@2
    displayName: 'arm下のyamlファイル名を変更'
    inputs:
      targetType: inline
      script: |
        copy-item -path $(Build.ArtifactStagingDirectory)/build/arm/azure-pipelines_dev.yml -destination $(Build.ArtifactStagingDirectory)/build/arm/azure-pipelines.yml
        remove-item $(Build.ArtifactStagingDirectory)/build/arm/azure-pipelines_dev.yml
  # armディレクトリから必要ファイルをコピー2
  - task: CopyFiles@2
    displayName: 'arm/deploy_settings下のファイルをコピー'
    inputs:
      SourceFolder: arm/deploy_settings
      Contents: |
        stg001_variables.ps1
        prd001_variables.ps1
      TargetFolder: $(Build.ArtifactStagingDirectory)/build/arm/deploy_settings
  # deployディレクトリから不要ファイルを削除
  - task: DeleteFiles@1
    inputs:
      SourceFolder: deploy
      Contents: |
        app/settings/dev_variables.ps1
        app/settings/infra_variables.ps1
        app/azure-pipeline.files.dev.yml
        app/azure-pipeline.main.dev.yml
        app/azure-pipeline.main.infra.yml
        app/azure-pipeline.main.yml
        app/azure-pipeline.files.infra.yml
        azfunc/azure-pipeline.yml
        azfunc/settings/dev_variables.ps1
        azfunc/settings/infra_variables.ps1
        mec*
        param-files/variables.dev.js
        param-files/variables.infra.js
        param-files/appsettings.dev.json
        param-files/appsettings.infra.json
        
  # deployディレクトリから必要ファイルwork/deployへをコピー
  - task: CopyFiles@2
    displayName: 'deploy下のファイルをコピー'
    inputs:
      SourceFolder: deploy
      Contents: '**/*'
      TargetFolder: $(Build.ArtifactStagingDirectory)/build/deploy

  - task: CopyFiles@2
    displayName: 'manifest/直下のファイルをコピー'
    inputs:
      SourceFolder: manifest
      Contents: |
        production/*
        staging/*
      TargetFolder: $(Build.ArtifactStagingDirectory)/build/manifest

  # アプリのビルド結果をアーティファクトとしてpublish
  - task: PublishPipelineArtifact@1
    displayName: 'アプリのビルド結果をアーティファクト名dropとしてpublish'
    inputs:
      targetPath: '$(Build.ArtifactStagingDirectory)/build'
      artifact: 'drop'
