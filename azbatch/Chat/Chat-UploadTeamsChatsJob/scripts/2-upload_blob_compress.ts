import { BlobServiceClient } from "@azure/storage-blob";
import path from "path";
import fs from "fs";
import dotenv from "dotenv";

dotenv.config();

// Constants
const ROOT_DIR = process.cwd(); // Current working directory
const FILENAME = process.env.CHAT_BATCH_JOB_NAME ? process.env.CHAT_BATCH_JOB_NAME + ".zip" : "";
const ZIP_FILE = path.join(ROOT_DIR, FILENAME);
const storageConnectionString = process.env.STORAGE_CONNECTION_STRING ?? "";
const storageContainer = process.env.STORAGE_BATCH_CONTAINERNAME ?? "";

/**
 * Uploads the zip file to Azure Blob Storage
 * @returns Promise with the URL of the uploaded blob
 */
async function uploadZipToBlob(): Promise<string> {
  console.log(`Uploading ${FILENAME} to Azure Blob Storage...`);

  // Validate environment variables
  if (!storageConnectionString) {
    throw new Error("STORAGE_CONNECTION_STRING environment variable is not set");
  }
  
  if (!storageContainer) {
    throw new Error("STORAGE_BATCH_CONTAINERNAME environment variable is not set");
  }
  
  if (!FILENAME) {
    throw new Error("STORAGE_BATCH_FILENAME environment variable is not set");
  }
  
  // Check if the zip file exists
  if (!fs.existsSync(ZIP_FILE)) {
    throw new Error(`Zip file not found: ${ZIP_FILE}. Run build script first.`);
  }

  try {
    // Initialize blob service client
    const blobServiceClient = BlobServiceClient.fromConnectionString(storageConnectionString);
    const containerClient = blobServiceClient.getContainerClient(storageContainer);

    // Create container if it doesn't exist
    await containerClient.createIfNotExists();
    console.log(`Container ${storageContainer} exists or was created.`);

    // Upload the zip file
    const blobName = FILENAME;
    const blockBlobClient = containerClient.getBlockBlobClient(blobName);

    console.log(`Uploading: ${blobName}...`);
    await blockBlobClient.uploadFile(ZIP_FILE);

    console.log(`Successfully uploaded to: ${blockBlobClient.url}`);
    
    return blockBlobClient.url;
  } catch (error) {
    console.error("Error uploading to blob storage:", error);
    throw error;
  }
}

/**
 * Main function to run the upload process
 */
async function main(): Promise<void> {
  try {
    console.log("Starting upload process...");
    console.log(`Zip file path: ${ZIP_FILE}`);

    // Upload zip file to blob storage
    const uploadUrl = await uploadZipToBlob();

    console.log("Upload process completed successfully!");
    console.log(`Zip file uploaded to: ${uploadUrl}`);
  } catch (error) {
    console.error("Error:", error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

// Execute the main function
main().catch((error: unknown) => {
  console.error("Error in main function:", error);
  process.exit(1);
});