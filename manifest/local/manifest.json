{"$schema": "https://developer.microsoft.com/json-schemas/teams/v1.12/MicrosoftTeams.schema.json", "manifestVersion": "1.12", "version": "4.0.0", "isFullScreen": true, "id": "6b399905-b6c1-4abe-a15c-e694024d9907", "packageName": "com.avanade.attane.local", "developer": {"name": "Avanade", "websiteUrl": "https://localhost:3000", "privacyUrl": "https://privacy.microsoft.com/ja-jp/privacystatement", "termsOfUseUrl": "https://azure.microsoft.com/ja-jp/support/legal/"}, "icons": {"color": "atTane_logo.png", "outline": "atTane_outline.png"}, "name": {"short": "atTane", "full": "atTane"}, "description": {"short": "attaneアプリについて", "full": "三菱地所の様々な場所にある情報を横断的に検索することができ、探したいものがすぐに見つかるアプリです。\n\n2023年9月に三菱地所社員向けに提供を開始しました。\n\n Ver.4.0.0では、以下の機能が追加されました。\n\n・アプリのプロモーション通知が届くようになりました"}, "accentColor": "#e45653", "activities": {"activityTypes": [{"type": "company", "description": "attane<PERSON>プリ", "templateText": "{name}"}]}, "staticTabs": [{"entityId": "attane_tab", "scopes": ["personal"], "context": ["personalTab", "channelTab"], "name": "attane<PERSON>プリ", "contentUrl": "https://localhost:3000/001/", "websiteUrl": "https://localhost:3000/001/", "searchUrl": "https://localhost:3000/001/"}], "permissions": ["identity", "messageTeamMembers"], "validDomains": ["localhost:3000", "localhost"], "webApplicationInfo": {"id": "6b399905-b6c1-4abe-a15c-e694024d9907", "resource": "api://localhost:3000/6b399905-b6c1-4abe-a15c-e694024d9907"}}