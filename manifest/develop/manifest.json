{"$schema": "https://developer.microsoft.com/json-schemas/teams/v1.12/MicrosoftTeams.schema.json", "manifestVersion": "1.12", "version": "3.0.2", "isFullScreen": true, "id": "5373bead-18df-4ae9-8b7e-9ffddaec51ba", "packageName": "com.avanade.teamsp.develop", "developer": {"name": "Avanade", "websiteUrl": "https://www.avanade.com/ja-jp", "privacyUrl": "https://privacy.microsoft.com/ja-jp/privacystatement", "termsOfUseUrl": "https://azure.microsoft.com/ja-jp/support/legal/"}, "icons": {"color": "atTane_logo.png", "outline": "atTane_outline.png"}, "name": {"short": "atTane-dev", "full": "atTane-dev"}, "description": {"short": "atTaneアプリについて", "full": "三菱地所の様々な場所にある情報を横断的に検索することができ、探したいものがすぐに見つかるアプリです。\n\n2023年9月に三菱地所社員向けに提供を開始しました。\n\n Ver.3.0.0では、以下の機能が追加されました。\n\n・検索結果の期間指定（From/To）フィルターができるようになりました"}, "accentColor": "#e45653", "activities": {"activityTypes": [{"type": "company", "description": "attane<PERSON>プリ", "templateText": "{name}"}]}, "staticTabs": [{"entityId": "attane_tab", "scopes": ["personal"], "context": ["personalTab", "channelTab"], "name": "(dev)atTane", "contentUrl": "https://fd-attane-dev-002.azurefd.net/mec/", "websiteUrl": "https://fd-attane-dev-002.azurefd.net/mec/", "searchUrl": "https://fd-attane-dev-002.azurefd.net/mec/"}], "permissions": ["identity", "messageTeamMembers"], "validDomains": ["https://fd-attane-dev-002.azurefd.net"], "webApplicationInfo": {"id": "5373bead-18df-4ae9-8b7e-9ffddaec51ba", "resource": "api://fd-attane-dev-002.azurefd.net/5373bead-18df-4ae9-8b7e-9ffddaec51ba"}}