@import '../../../../styles/variables';

.teams-setting-tabs-container {
  margin-bottom: 16px;
}

.teams-setting-tabs-content {
  display: flex;
  flex-flow: row nowrap;
  justify-content: flex-start;
  align-items: flex-end;
  height: 32px;

  /* Fluent UIのMenuコンポーネントの余白を打ち消す */
  .ui-menu {
    margin: 0;
    padding: 0;
  }

  .ui-menu__itemwrapper {
    margin: 0;
    padding: 0;
  }

  &.disabled {
    opacity: 0.6;
    pointer-events: none;

    .tab-button {
      cursor: not-allowed;
    }
  }

  a.ui-menu__item {
    color: var(--color-guide-foreground-2);
    cursor: pointer;
    transition: border-color 0.2s, color 0.2s, box-shadow 0.2s, background-color 0.2s;
    border-radius: 4px 4px 0 0;
    padding: 8px 16px 10px;
    margin: 0;
    min-width: 80px;
    text-align: center;

    /* マージンを0にしてタブをくっつける */
    border-bottom: 1px solid var(--color-guide-foreground-6);
    position: relative;
    box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.1);
    background-color: rgba(0, 0, 0, 0.05);

    /* 非アクティブタブを暗く */
    &[aria-selected='true'] {
      font-weight: bold;
      color: var(--color-guide-brand-main-foreground);
      box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.15);
      background-color: var(--color-guide-background);
      /* アクティブタブは背景色と同じに */
      z-index: 1;
      /* 下線を削除 */
      border-bottom: none;
    }

    &:hover:not([aria-selected='true']) {
      background-color: rgba(0, 0, 0, 0.1);
      color: var(--color-guide-foreground-1);
    }

    &:focus {
      outline: 2px solid var(--color-guide-brand-main-foreground);
      outline-offset: -2px;
    }

    .teams-setting-tabs-count {
      margin-left: 4px;
      font-size: 0.9em;
      opacity: 0.8;
    }
  }
}

/* ダークテーマ対応 */
@media (prefers-color-scheme: dark) {
  .teams-setting-tabs-content {
    a.ui-menu__item {
      background-color: rgba(255, 255, 255, 0.05);

      &:hover:not([aria-selected='true']) {
        background-color: rgba(255, 255, 255, 0.1);
      }
    }
  }
}
