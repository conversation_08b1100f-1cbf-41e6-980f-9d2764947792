version: 2

updates:
  - directory: /web
    open-pull-requests-limit: 2
    package-ecosystem: npm
    rebase-strategy: disabled
    schedule:
      interval: monthly
    ignore:
      - dependency-name: "*pkgs.dev.azure.com/tokyodis-newportal/SharedComponents/_packaging/Avanade.Teams/npm/registry/*"
      - dependency-name: "@avanade-teams/app-insights-reporter"
      - dependency-name: "@avanade-teams/auth"
      - dependency-name: "@avanade-teams/deeplink"
      - dependency-name: "@avanade-teams/teams-info"
